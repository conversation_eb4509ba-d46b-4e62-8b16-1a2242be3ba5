[debug] [2025-06-06T23:31:52.365Z] ----------------------------------------------------------------------
[debug] [2025-06-06T23:31:52.368Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions
[debug] [2025-06-06T23:31:52.369Z] CLI Version:   14.1.0
[debug] [2025-06-06T23:31:52.369Z] Platform:      win32
[debug] [2025-06-06T23:31:52.369Z] Node Version:  v22.14.0
[debug] [2025-06-06T23:31:52.370Z] Time:          Sat Jun 07 2025 00:31:52 GMT+0100 (West Africa Standard Time)
[debug] [2025-06-06T23:31:52.370Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-06T23:31:52.722Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-06T23:31:52.723Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-06T23:31:52.723Z] [iam] checking project tradetracker-30ec1 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-06T23:31:52.725Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:52.725Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:52.726Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions [none]
[debug] [2025-06-06T23:31:52.727Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:31:52.727Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-06T23:31:53.784Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions 200
[debug] [2025-06-06T23:31:53.784Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-06T23:31:53.785Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:53.785Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:53.785Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/tradetracker-30ec1/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-06-06T23:31:53.785Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/tradetracker-30ec1/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-06-06T23:31:54.728Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/tradetracker-30ec1/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-06-06T23:31:54.729Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/tradetracker-30ec1/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'tradetracker-30ec1'...
[info] 
[info] i  deploying functions 
[debug] [2025-06-06T23:31:54.741Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:54.742Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:54.742Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1 [none]
[debug] [2025-06-06T23:31:55.003Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1 200
[debug] [2025-06-06T23:31:55.004Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1 {"projectNumber":"***********","projectId":"tradetracker-30ec1","lifecycleState":"ACTIVE","name":"TradeTracker","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-04-09T10:10:50.641897Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-06-06T23:31:55.006Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:55.006Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:55.006Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:55.006Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-06-06T23:31:55.006Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:55.006Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-06-06T23:31:55.007Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:55.007Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:55.007Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudfunctions.googleapis.com [none]
[debug] [2025-06-06T23:31:55.007Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:31:55.008Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/runtimeconfig.googleapis.com [none]
[debug] [2025-06-06T23:31:55.008Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:31:55.010Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudbuild.googleapis.com [none]
[debug] [2025-06-06T23:31:55.010Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudbuild.googleapis.com x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:31:55.011Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/artifactregistry.googleapis.com [none]
[debug] [2025-06-06T23:31:55.011Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/artifactregistry.googleapis.com x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:31:56.125Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudfunctions.googleapis.com 200
[debug] [2025-06-06T23:31:56.126Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudfunctions.googleapis.com [omitted]
[info] +  functions: required API cloudfunctions.googleapis.com is enabled 
[debug] [2025-06-06T23:31:56.129Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/runtimeconfig.googleapis.com 200
[debug] [2025-06-06T23:31:56.129Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/runtimeconfig.googleapis.com [omitted]
[debug] [2025-06-06T23:31:56.143Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudbuild.googleapis.com 200
[debug] [2025-06-06T23:31:56.143Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudbuild.googleapis.com [omitted]
[info] +  functions: required API cloudbuild.googleapis.com is enabled 
[debug] [2025-06-06T23:31:56.168Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/artifactregistry.googleapis.com 200
[debug] [2025-06-06T23:31:56.168Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/artifactregistry.googleapis.com [omitted]
[info] +  artifactregistry: required API artifactregistry.googleapis.com is enabled 
[debug] [2025-06-06T23:31:56.179Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:56.179Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:56.179Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/tradetracker-30ec1/adminSdkConfig [none]
[debug] [2025-06-06T23:31:56.933Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/tradetracker-30ec1/adminSdkConfig 200
[debug] [2025-06-06T23:31:56.934Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/tradetracker-30ec1/adminSdkConfig {"projectId":"tradetracker-30ec1","storageBucket":"tradetracker-30ec1.firebasestorage.app"}
[debug] [2025-06-06T23:31:56.934Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:56.934Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:31:56.934Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tradetracker-30ec1/configs [none]
[debug] [2025-06-06T23:31:57.541Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tradetracker-30ec1/configs 200
[debug] [2025-06-06T23:31:57.542Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/tradetracker-30ec1/configs {}
[debug] [2025-06-06T23:31:57.544Z] Validating nodejs source
[warn] !  functions: Runtime Node.js 18 was deprecated on 2025-04-30 and will be decommissioned on 2025-10-31, after which you will not be able to deploy without upgrading. Consider upgrading now to avoid disruption. See https://cloud.google.com/functions/docs/runtime-support for full details on the lifecycle policy 
[warn] !  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[warn] !  functions: Please note that there will be breaking changes when you upgrade. 
[debug] [2025-06-06T23:31:59.444Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "build": "tsc",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^11.11.1",
    "firebase-functions": "^4.9.0"
  },
  "devDependencies": {
    "typescript": "^4.9.0"
  },
  "private": true
}
[debug] [2025-06-06T23:31:59.445Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[info] i  functions: You are using a version of firebase-functions SDK (4.9.0) that does not have support for the newest Firebase Extensions features. Please update firebase-functions SDK to >=5.1.0 to use them correctly 
[debug] [2025-06-06T23:31:59.447Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-06T23:31:59.467Z] Found firebase-functions binary at 'G:\Projects\trade-tracker\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8317

[debug] [2025-06-06T23:32:00.583Z] Got response from /__/functions.yaml {"endpoints":{"cleanupExpiredCalendars":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"scheduleTrigger":{"schedule":"every day 02:00","retryConfig":{"retryCount":null,"maxDoublings":null,"maxRetryDuration":null,"maxBackoffDuration":null,"minBackoffDuration":null},"timeZone":"UTC"},"labels":{},"entryPoint":"cleanupExpiredCalendars"},"manualCleanupExpiredCalendars":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"httpsTrigger":{},"entryPoint":"manualCleanupExpiredCalendars"},"onTradeChanged":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"eventTrigger":{"eventType":"providers/cloud.firestore/eventTypes/document.update","eventFilters":{"resource":"projects/tradetracker-30ec1/databases/(default)/documents/calendars/{calendarId}/years/{yearId}"},"retry":false},"labels":{},"entryPoint":"onTradeChanged"},"cleanupDeletedCalendar":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"eventTrigger":{"eventType":"providers/cloud.firestore/eventTypes/document.delete","eventFilters":{"resource":"projects/tradetracker-30ec1/databases/(default)/documents/calendars/{calendarId}"},"retry":false},"labels":{},"entryPoint":"cleanupDeletedCalendar"},"updateTag":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"httpsTrigger":{},"entryPoint":"updateTag"},"maintainCalendarTags":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"eventTrigger":{"eventType":"providers/cloud.firestore/eventTypes/document.write","eventFilters":{"resource":"projects/tradetracker-30ec1/databases/(default)/documents/calendars/{calendarId}/years/{yearId}"},"retry":false},"labels":{},"entryPoint":"maintainCalendarTags"}},"specVersion":"v1alpha1","requiredAPIs":[{"api":"cloudscheduler.googleapis.com","reason":"Needed for scheduled functions."}]}
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged G:\Projects\trade-tracker\functions (59.93 KB) for uploading 
[debug] [2025-06-06T23:32:04.783Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:04.783Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:04.783Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/-/functions [none]
[debug] [2025-06-06T23:32:05.675Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/-/functions 200
[debug] [2025-06-06T23:32:05.676Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/-/functions {"functions":[{"name":"projects/tradetracker-30ec1/locations/us-central1/functions/onTradeChanged","eventTrigger":{"eventType":"providers/cloud.firestore/eventTypes/document.update","resource":"projects/tradetracker-30ec1/databases/(default)/documents/calendars/{calendarId}/years/{yearId}","service":"firestore.googleapis.com","failurePolicy":{}},"status":"ACTIVE","entryPoint":"onTradeChanged","timeout":"60s","availableMemoryMb":256,"serviceAccountEmail":"<EMAIL>","updateTime":"2025-06-06T23:29:21.673Z","versionId":"5","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"ac4ce0296934281b082339d390b10e21ef269a65"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/0ca57d84-0066-40b4-b7a1-4a4b300649a6.zip","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/onTradeChanged"},"runtime":"nodejs18","maxInstances":3000,"ingressSettings":"ALLOW_ALL","buildId":"bf6e1905-f2e6-426f-b45e-bbb274fe6db1","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"buildName":"projects/***********/locations/us-central1/builds/bf6e1905-f2e6-426f-b45e-bbb274fe6db1","dockerRegistry":"ARTIFACT_REGISTRY","automaticUpdatePolicy":{},"buildServiceAccount":"projects/tradetracker-30ec1/serviceAccounts/<EMAIL>","satisfiesPzi":true},{"name":"projects/tradetracker-30ec1/locations/us-central1/functions/cleanupExpiredCalendars","eventTrigger":{"eventType":"google.pubsub.topic.publish","resource":"projects/tradetracker-30ec1/topics/firebase-schedule-cleanupExpiredCalendars-us-central1","service":"pubsub.googleapis.com","failurePolicy":{}},"status":"ACTIVE","entryPoint":"cleanupExpiredCalendars","timeout":"60s","availableMemoryMb":256,"serviceAccountEmail":"<EMAIL>","updateTime":"2025-06-06T23:29:07.199084733Z","versionId":"1","labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"ac4ce0296934281b082339d390b10e21ef269a65"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/0ca57d84-0066-40b4-b7a1-4a4b300649a6.zip","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/cleanupExpiredCalendars"},"runtime":"nodejs18","maxInstances":3000,"ingressSettings":"ALLOW_ALL","buildId":"bf6e1905-f2e6-426f-b45e-bbb274fe6db1","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"buildName":"projects/***********/locations/us-central1/builds/bf6e1905-f2e6-426f-b45e-bbb274fe6db1","dockerRegistry":"ARTIFACT_REGISTRY","automaticUpdatePolicy":{},"buildServiceAccount":"projects/tradetracker-30ec1/serviceAccounts/<EMAIL>","satisfiesPzi":true},{"name":"projects/tradetracker-30ec1/locations/us-central1/functions/cleanupDeletedCalendar","eventTrigger":{"eventType":"providers/cloud.firestore/eventTypes/document.delete","resource":"projects/tradetracker-30ec1/databases/(default)/documents/calendars/{calendarId}","service":"firestore.googleapis.com","failurePolicy":{}},"status":"ACTIVE","entryPoint":"cleanupDeletedCalendar","timeout":"60s","availableMemoryMb":256,"serviceAccountEmail":"<EMAIL>","updateTime":"2025-06-06T23:29:21.761Z","versionId":"7","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"ac4ce0296934281b082339d390b10e21ef269a65"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/0ca57d84-0066-40b4-b7a1-4a4b300649a6.zip","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/cleanupDeletedCalendar"},"runtime":"nodejs18","maxInstances":3000,"ingressSettings":"ALLOW_ALL","buildId":"bf6e1905-f2e6-426f-b45e-bbb274fe6db1","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"buildName":"projects/***********/locations/us-central1/builds/bf6e1905-f2e6-426f-b45e-bbb274fe6db1","dockerRegistry":"ARTIFACT_REGISTRY","automaticUpdatePolicy":{},"buildServiceAccount":"projects/tradetracker-30ec1/serviceAccounts/<EMAIL>","satisfiesPzi":true},{"name":"projects/tradetracker-30ec1/locations/us-central1/functions/updateTag","httpsTrigger":{"url":"https://us-central1-tradetracker-30ec1.cloudfunctions.net/updateTag","securityLevel":"SECURE_ALWAYS"},"status":"ACTIVE","entryPoint":"updateTag","timeout":"60s","availableMemoryMb":256,"serviceAccountEmail":"<EMAIL>","updateTime":"2025-06-06T23:29:24.775Z","versionId":"5","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"ac4ce0296934281b082339d390b10e21ef269a65"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/0ca57d84-0066-40b4-b7a1-4a4b300649a6.zip","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/updateTag"},"runtime":"nodejs18","ingressSettings":"ALLOW_ALL","buildId":"bf6e1905-f2e6-426f-b45e-bbb274fe6db1","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"buildName":"projects/***********/locations/us-central1/builds/bf6e1905-f2e6-426f-b45e-bbb274fe6db1","dockerRegistry":"ARTIFACT_REGISTRY","automaticUpdatePolicy":{},"buildServiceAccount":"projects/tradetracker-30ec1/serviceAccounts/<EMAIL>","satisfiesPzi":true},{"name":"projects/tradetracker-30ec1/locations/us-central1/functions/maintainCalendarTags","eventTrigger":{"eventType":"providers/cloud.firestore/eventTypes/document.write","resource":"projects/tradetracker-30ec1/databases/(default)/documents/calendars/{calendarId}/years/{yearId}","service":"firestore.googleapis.com","failurePolicy":{}},"status":"ACTIVE","entryPoint":"maintainCalendarTags","timeout":"60s","availableMemoryMb":256,"serviceAccountEmail":"<EMAIL>","updateTime":"2025-06-06T23:29:21.597Z","versionId":"2","labels":{"firebase-functions-hash":"ac4ce0296934281b082339d390b10e21ef269a65","deployment-tool":"cli-firebase"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/0ca57d84-0066-40b4-b7a1-4a4b300649a6.zip","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/maintainCalendarTags"},"runtime":"nodejs18","maxInstances":3000,"ingressSettings":"ALLOW_ALL","buildId":"bf6e1905-f2e6-426f-b45e-bbb274fe6db1","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"buildName":"projects/***********/locations/us-central1/builds/bf6e1905-f2e6-426f-b45e-bbb274fe6db1","dockerRegistry":"ARTIFACT_REGISTRY","automaticUpdatePolicy":{},"buildServiceAccount":"projects/tradetracker-30ec1/serviceAccounts/<EMAIL>","satisfiesPzi":true}]}
[debug] [2025-06-06T23:32:05.679Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:05.679Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:05.680Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/tradetracker-30ec1/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-06-06T23:32:06.725Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/tradetracker-30ec1/locations/-/functions 200
[debug] [2025-06-06T23:32:06.725Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/tradetracker-30ec1/locations/-/functions {}
[info] i  functions: ensuring required API cloudscheduler.googleapis.com is enabled... 
[debug] [2025-06-06T23:32:06.743Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:06.743Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:06.743Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudscheduler.googleapis.com [none]
[debug] [2025-06-06T23:32:06.743Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudscheduler.googleapis.com x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:32:07.857Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudscheduler.googleapis.com 200
[debug] [2025-06-06T23:32:07.857Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/tradetracker-30ec1/services/cloudscheduler.googleapis.com [omitted]
[info] +  functions: required API cloudscheduler.googleapis.com is enabled 
[debug] [2025-06-06T23:32:07.859Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:07.859Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:07.859Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1 [none]
[debug] [2025-06-06T23:32:08.848Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1 200
[debug] [2025-06-06T23:32:08.848Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1 {"projectNumber":"***********","projectId":"tradetracker-30ec1","lifecycleState":"ACTIVE","name":"TradeTracker","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-04-09T10:10:50.641897Z"}
[debug] [2025-06-06T23:32:08.849Z] [functions] found 1 new HTTP functions, testing setIamPolicy permission...
[debug] [2025-06-06T23:32:08.849Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:08.849Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:08.849Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions [none]
[debug] [2025-06-06T23:32:08.849Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-06T23:32:08.849Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions {"permissions":["cloudfunctions.functions.setIamPolicy"]}
[debug] [2025-06-06T23:32:09.098Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions 200
[debug] [2025-06-06T23:32:09.098Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions {"permissions":["cloudfunctions.functions.setIamPolicy"]}
[debug] [2025-06-06T23:32:09.098Z] [functions] found setIamPolicy permission, proceeding with deploy
[debug] [2025-06-06T23:32:09.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:09.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:09.101Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions:generateUploadUrl [none]
[debug] [2025-06-06T23:32:09.101Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions:generateUploadUrl {}
[debug] [2025-06-06T23:32:09.778Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions:generateUploadUrl 200
[debug] [2025-06-06T23:32:09.778Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions:generateUploadUrl {"uploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D"}
[debug] [2025-06-06T23:32:09.779Z] >>> [apiv2][query] PUT https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip GoogleAccessId=service-***********%40gcf-admin-robot.iam.gserviceaccount.com&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D
[debug] [2025-06-06T23:32:09.779Z] >>> [apiv2][body] PUT https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip [stream]
[debug] [2025-06-06T23:32:10.639Z] <<< [apiv2][status] PUT https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip 200
[debug] [2025-06-06T23:32:10.640Z] <<< [apiv2][body] PUT https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip [omitted]
[info] +  functions: functions folder uploaded successfully 
[info] i  functions: creating Node.js 18 (1st Gen) function manualCleanupExpiredCalendars(us-central1)... 
[info] i  functions: updating Node.js 18 (1st Gen) function cleanupExpiredCalendars(us-central1)... 
[info] i  functions: updating Node.js 18 (1st Gen) function onTradeChanged(us-central1)... 
[info] i  functions: updating Node.js 18 (1st Gen) function cleanupDeletedCalendar(us-central1)... 
[info] i  functions: updating Node.js 18 (1st Gen) function updateTag(us-central1)... 
[info] i  functions: updating Node.js 18 (1st Gen) function maintainCalendarTags(us-central1)... 
[debug] [2025-06-06T23:32:10.659Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:10.660Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:10.660Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions [none]
[debug] [2025-06-06T23:32:10.661Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions {"name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","entryPoint":"manualCleanupExpiredCalendars","runtime":"nodejs18","dockerRegistry":"ARTIFACT_REGISTRY","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"minInstances":null,"maxInstances":null,"ingressSettings":null,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"serviceAccountEmail":null,"availableMemoryMb":null,"timeout":null,"vpcConnector":null,"vpcConnectorEgressSettings":null,"buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}}
[debug] [2025-06-06T23:32:11.829Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions 200
[debug] [2025-06-06T23:32:11.829Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/tradetracker-30ec1/locations/us-central1/functions {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","updateTime":"2025-06-06T23:32:11.618276508Z"}}
[debug] [2025-06-06T23:32:11.830Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:11.830Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:11.830Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:12.141Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:12.142Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","updateTime":"2025-06-06T23:32:11.618276508Z"}}
[debug] [2025-06-06T23:32:12.653Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:12.654Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:12.654Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:12.654Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:12.991Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:12.991Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z"}}
[debug] [2025-06-06T23:32:13.993Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:13.994Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:13.994Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:13.995Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:14.313Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:14.314Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z"}}
[debug] [2025-06-06T23:32:16.315Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:16.316Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:16.316Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:16.317Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:16.595Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:16.595Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z","buildId":"873a5277-0eac-4486-9e72-f1068692411a","buildName":"projects/***********/locations/us-central1/builds/873a5277-0eac-4486-9e72-f1068692411a"}}
[debug] [2025-06-06T23:32:20.607Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:20.608Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:20.608Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:20.608Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:21.354Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:21.354Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z","buildId":"873a5277-0eac-4486-9e72-f1068692411a","buildName":"projects/***********/locations/us-central1/builds/873a5277-0eac-4486-9e72-f1068692411a"}}
[debug] [2025-06-06T23:32:29.357Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:29.358Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:29.358Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:29.358Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:29.910Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:29.910Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z","buildId":"873a5277-0eac-4486-9e72-f1068692411a","buildName":"projects/***********/locations/us-central1/builds/873a5277-0eac-4486-9e72-f1068692411a"}}
[debug] [2025-06-06T23:32:39.913Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:39.914Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:39.914Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:39.914Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:40.541Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:40.542Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z","buildId":"873a5277-0eac-4486-9e72-f1068692411a","buildName":"projects/***********/locations/us-central1/builds/873a5277-0eac-4486-9e72-f1068692411a"}}
[debug] [2025-06-06T23:32:50.548Z] [create-default-us-central1-manualCleanupExpiredCalendars] Retrying task index 0
[debug] [2025-06-06T23:32:50.549Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:50.549Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-06T23:32:50.549Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU [none]
[debug] [2025-06-06T23:32:51.095Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU 200
[debug] [2025-06-06T23:32:51.096Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU {"name":"operations/cHJvamVjdHMvdHJhZGV0cmFja2VyLTMwZWMxL2xvY2F0aW9ucy91cy1jZW50cmFsMS9vcGVyYXRpb25zL29wZXJhdGlvbi0xNzQ5MjUyNzMwODM0LTYzNmVmYTNhNGNmNjAtZmQyZTI0OTMtMmZmN2MxOWU","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v1.OperationMetadataV1","target":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","type":"CREATE_FUNCTION","request":{"@type":"type.googleapis.com/google.cloud.functions.v1.CloudFunction","name":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars","httpsTrigger":{"securityLevel":"SECURE_ALWAYS"},"entryPoint":"manualCleanupExpiredCalendars","updateTime":"1970-01-01T00:00:00Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"2119a5972d4a2b9d4621e2ac0c6280225fefe737"},"sourceUploadUrl":"https://storage.googleapis.com/uploads-*************.us-central1.cloudfunctions.appspot.com/9f76be05-e2e9-4fac-b59c-c6ef5c58da56.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=Pj%2F2BDkvIe6%2FzOOW52EJFf6xGV0qozrHKjXcecDga6S2ip6ZDhqQ88qPzw3Q2mtsnZd1sGDddm0On4hKD0Sbt54BI11lWCc1IWYt6vEujy4ouqhmHX%2B591kLn93%2FTzekOypt1%2FQgxvhZ5tTPaUjw4OQ5L1oWhSXVakRz7JFYxpKot8vwTa%2FYtmutL9acwqKdUmkeIZeEVX5NUVy7GxvT1DXRnLV9NbhUfgtdzOHhC2eTcLn8kpqnKYpzI%2B7icRyloyrQHrFq0TUntXNDiT4iY42e%2FfOS9HC6lyeMzdOfmXaxpROzKUZmnej9unGhKgi629YHTMpx8JWMD053kwxPPA%3D%3D","environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"tradetracker-30ec1\",\"storageBucket\":\"tradetracker-30ec1.firebasestorage.app\"}","GCLOUD_PROJECT":"tradetracker-30ec1","EVENTARC_CLOUD_EVENT_SOURCE":"projects/tradetracker-30ec1/locations/us-central1/functions/manualCleanupExpiredCalendars"},"runtime":"nodejs18","buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRegistry":"ARTIFACT_REGISTRY"},"versionId":"1","updateTime":"2025-06-06T23:32:11.618276508Z","buildId":"873a5277-0eac-4486-9e72-f1068692411a","buildName":"projects/***********/locations/us-central1/builds/873a5277-0eac-4486-9e72-f1068692411a"}}
